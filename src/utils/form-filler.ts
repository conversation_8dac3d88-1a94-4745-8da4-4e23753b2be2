import { FormField, FormContent, Fill<PERSON><PERSON><PERSON><PERSON>back, Logger } from '../types/common'
import { FIELD_MAPPINGS, STYLES } from '../constants'
import { detectFormFields, isVisibleElement } from './form-detector'

// 创建优化的 Logger 实例
const createLogger = (): Logger => {
  return {
    _log: (type, category, message, data = null) => {
      if (process.env.NODE_ENV === 'production' && type !== 'error') return
      
      const icons = {
        info: 'ℹ️',
        success: '✅',
        error: '❌',
        debug: '🔍'
      }
      
      const timestamp = new Date().toISOString().split('T')[1].split('.')[0]
      console.log(`[Formify ${timestamp}] ${icons[type]} [${category}] ${message}`, data || '')
    },
    
    info: function(category: string, message: string, data?: any) {
      this._log('info', category, message, data)
    },
    
    success: function(category: string, message: string, data?: any) {
      this._log('success', category, message, data)
    },
    
    error: function(message: string, error: any) {
      console.error(`[Formify] ❌ ${message}`, error)
    },
    
    debug: function(category: string, message: string, data?: any) {
      this._log('debug', category, message, data)
    }
  }
}

const Logger = createLogger()

// 全局变量来跟踪样式标签
let generatingStyleTag: HTMLStyleElement | null = null

/**
 * 显示生成中的视觉效果
 */
export function showGeneratingEffect(): void {
  try {
    // 如果已经有样式标签，先移除
    if (generatingStyleTag && generatingStyleTag.parentNode) {
      generatingStyleTag.parentNode.removeChild(generatingStyleTag)
    }

    // 检测表单字段
    const formFields = detectFormFields()
    if (!formFields.length) {
      Logger.debug('effect', 'No form fields detected for showing effect')
      return
    }

    // 添加加载动画样式
    generatingStyleTag = document.createElement('style')
    generatingStyleTag.textContent = STYLES.LOADING_ANIMATION
    document.head.appendChild(generatingStyleTag)

    // 给所有表单字段添加加载动画
    formFields.forEach(field => {
      if (field.element) {
        field.element.classList.add(STYLES.LOADING_CLASS)
      }
    })

    Logger.debug('effect', 'Added generating effect to form fields')
  } catch (error) {
    Logger.error('Error showing generating effect:', error)
  }
}

/**
 * 移除生成中的视觉效果
 */
export function removeGeneratingEffect(): void {
  try {
    // 移除所有加载动画
    document.querySelectorAll(`.${STYLES.LOADING_CLASS}`).forEach(el => {
      el.classList.remove(STYLES.LOADING_CLASS)
    })

    // 移除样式标签
    if (generatingStyleTag && generatingStyleTag.parentNode) {
      generatingStyleTag.parentNode.removeChild(generatingStyleTag)
      generatingStyleTag = null
    }

    Logger.debug('effect', 'Removed generating effect from form fields')
  } catch (error) {
    Logger.error('Error removing generating effect:', error)
  }
}

/**
 * 安全地设置 HTML 内容
 */
function safeSetInnerHTML(element: HTMLElement, content: string): void {
  try {
    // 基本的 XSS 防护 - 移除 script 标签和危险属性
    const sanitizedContent = content
      .replace(/<script[^>]*>.*?<\/script>/gi, '')
      .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
      .replace(/javascript:/gi, '')
    
    element.innerHTML = sanitizedContent
  } catch (error) {
    Logger.error('Error setting innerHTML safely:', error)
    // 降级到 textContent
    element.textContent = content
  }
}

/**
 * 智能内容匹配函数
 */
function findValueByLanguage(obj: any, key: string): string | undefined {
  if (!key || !obj) return undefined

  try {
    // 直接匹配
    if (obj[key] !== undefined) return obj[key]

    // 不区分大小写匹配
    const lowerKey = key.toLowerCase()
    const keyMatch = Object.keys(obj).find(k => k.toLowerCase() === lowerKey)
    if (keyMatch) return obj[keyMatch]

    // 部分匹配
    const partialMatch = Object.keys(obj).find(k =>
      k.toLowerCase().includes(lowerKey) || lowerKey.includes(k.toLowerCase())
    )
    if (partialMatch) return obj[partialMatch]

    // 智能匹配常见字段
    for (const [category, synonyms] of Object.entries(FIELD_MAPPINGS)) {
      if (synonyms.some(s => lowerKey.includes(s) || s.includes(lowerKey))) {
        const categoryMatch = Object.keys(obj).find(k =>
          synonyms.some(s => k.toLowerCase().includes(s))
        )
        if (categoryMatch) return obj[categoryMatch]
      }
    }

    return undefined
  } catch (error) {
    Logger.error('Error in findValueByLanguage:', error)
    return undefined
  }
}

/**
 * 填充 select 元素
 */
function fillSelectElement(select: HTMLSelectElement, content: string): void {
  try {
    const contentLower = content.toLowerCase().trim()
    const options = Array.from(select.options)

    // 多种匹配策略
    let matchedOption = options.find(option => option.value === content) ||
                       options.find(option => option.text === content) ||
                       options.find(option => 
                         option.value.toLowerCase() === contentLower ||
                         option.text.toLowerCase() === contentLower
                       ) ||
                       options.find(option =>
                         option.value.toLowerCase().includes(contentLower) ||
                         contentLower.includes(option.value.toLowerCase()) ||
                         option.text.toLowerCase().includes(contentLower) ||
                         contentLower.includes(option.text.toLowerCase())
                       )

    if (matchedOption) {
      select.value = matchedOption.value
      const event = new Event('change', { bubbles: true })
      select.dispatchEvent(event)
      Logger.debug('fill', `Selected option: ${matchedOption.text} (${matchedOption.value})`)
    } else {
      Logger.debug('fill', `No matching option found for: ${content}`)
    }
  } catch (error) {
    Logger.error('Error filling select element:', error)
  }
}

/**
 * 处理 React 组件的特殊填充方式
 */
function fillReactComponent(element: HTMLInputElement | HTMLTextAreaElement, content: string): boolean {
  try {
    const nativeValueSetter = Object.getOwnPropertyDescriptor(
      element instanceof HTMLInputElement ? window.HTMLInputElement.prototype : window.HTMLTextAreaElement.prototype,
      "value"
    )?.set

    if (nativeValueSetter) {
      nativeValueSetter.call(element, content)

      // 触发一系列事件来确保 React 状态更新
      const events = ['input', 'change', 'blur']
      events.forEach(eventType => {
        const event = new Event(eventType, {
          bubbles: true,
          composed: true,
          cancelable: true
        })
        element.dispatchEvent(event)
      })

      // 确保元素获得焦点
      element.focus()

      // 创建并触发输入事件
      const inputEvent = new InputEvent('input', {
        bubbles: true,
        cancelable: true,
        inputType: 'insertText',
        data: content
      })
      element.dispatchEvent(inputEvent)

      // 最后失去焦点
      element.blur()

      Logger.debug('fill', 'Filled React component')
      return true
    }
  } catch (error) {
    Logger.error('Error filling React component:', error)
  }
  return false
}

/**
 * 处理富文本编辑器
 */
function fillRichTextEditor(element: HTMLElement, content: string, field: FormField): boolean {
  try {
    // 检查是否是特定邮件客户端的编辑器
    const isGmailEditor = element.classList.contains('editable') ||
                          element.classList.contains('LW-avf') ||
                          element.closest('.Am.Al.editable') !== null ||
                          (element.getAttribute('role') === 'textbox' &&
                           element.closest('[aria-label="Message Body"]') !== null)

    const isOutlookEditor = element.getAttribute('role') === 'textbox' &&
                          (element.classList.contains('DziEn') ||
                           element.hasAttribute('aria-label') &&
                           (element.getAttribute('aria-label')?.includes('Message body') ||
                            element.getAttribute('aria-label')?.includes('正文')))

    // 聚焦元素
    element.focus()

    // 清空现有内容
    element.innerHTML = ''

    // 处理内容格式
    if (field.name === 'body' || field.name === 'content' || field.name === 'message') {
      // 将换行符转换为HTML标签
      const formattedContent = content.replace(/\n/g, '<br>')
      safeSetInnerHTML(element, formattedContent)
    } else {
      // 其他字段使用纯文本
      const textNode = document.createTextNode(content)
      element.appendChild(textNode)
    }

    // 触发必要的事件
    const events = ['input', 'change', 'blur']
    events.forEach(eventType => {
      const event = new Event(eventType, { bubbles: true, composed: true })
      element.dispatchEvent(event)
    })

    // 特殊处理收件人字段
    if ((field.name === 'to' || field.name === 'recipient') && isOutlookEditor) {
      setTimeout(() => {
        const enterEvent = new KeyboardEvent('keydown', {
          key: 'Enter',
          code: 'Enter',
          keyCode: 13,
          which: 13,
          bubbles: true,
          cancelable: true
        })
        element.dispatchEvent(enterEvent)
      }, 100)
    }

    Logger.debug('fill', `Filled rich text editor (${isGmailEditor ? 'Gmail' : isOutlookEditor ? 'Outlook' : 'Generic'})`)
    return true
  } catch (error) {
    Logger.error('Error filling rich text editor:', error)
    return false
  }
}

/**
 * 处理 iframe 编辑器
 */
function fillIframeEditor(iframe: HTMLIFrameElement, content: string): boolean {
  try {
    const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document

    if (iframeDocument && iframeDocument.body) {
      safeSetInnerHTML(iframeDocument.body, content)

      // 触发 iframe 内容变化事件
      const event = new Event('input', { bubbles: true })
      iframeDocument.body.dispatchEvent(event)

      Logger.debug('fill', 'Filled iframe editor content')
      return true
    }
  } catch (error) {
    Logger.error('Error filling iframe editor:', error)
  }
  return false
}

/**
 * 处理 GitHub 特定字段
 */
function fillGitHubField(element: HTMLElement, content: string, fieldName: string): boolean {
  try {
    Logger.debug('fill', `Processing GitHub field: ${fieldName}`, {
      elementType: element.tagName,
      elementId: element.id,
      elementClasses: element.className
    })

    if (fieldName === 'description' || fieldName === 'body') {
      if (element.tagName.toLowerCase() === 'textarea') {
        return fillReactComponent(element as HTMLTextAreaElement, content)
      } else if (element.getAttribute('role') === 'textbox' || element.getAttribute('contenteditable') === 'true') {
        element.focus()
        element.innerHTML = ''
        const formattedContent = content.replace(/\n/g, '<br>')
        safeSetInnerHTML(element, formattedContent)
        
        const events = ['input', 'change', 'blur']
        events.forEach(eventType => {
          const event = new Event(eventType, { bubbles: true, composed: true })
          element.dispatchEvent(event)
        })
        
        Logger.debug('fill', 'Filled GitHub textbox/contenteditable element')
        return true
      }
    } else if (fieldName === 'title') {
      if (element.tagName.toLowerCase() === 'input') {
        return fillReactComponent(element as HTMLInputElement, content)
      }
    }
  } catch (error) {
    Logger.error('Error filling GitHub element:', error)
  }
  return false
}

/**
 * 填充单个表单字段
 */
export function fillFieldContent(field: FormField, content: string): void {
  if (!field.element || !content) {
    Logger.debug('fill', `Skipping field fill due to missing element or content`, {
      hasElement: !!field.element,
      hasContent: !!content,
      fieldName: field.name
    })
    return
  }

  try {
    Logger.debug('fill', `Preparing to fill field: ${field.name}`, {
      content: content.substring(0, 50) + (content.length > 50 ? '...' : ''),
      elementDetails: {
        tagName: field.element.tagName,
        type: field.element instanceof HTMLInputElement ? field.element.type : 'not-input',
        classes: field.element.className,
        id: field.element.id,
        name: field.name,
        isVisible: isVisibleElement(field.element)
      }
    })

    // 记录填充前的值
    const previousValue = field.element instanceof HTMLInputElement || field.element instanceof HTMLTextAreaElement
      ? field.element.value
      : field.element.textContent

    // 特殊处理 GitHub 字段
    if (window.location.hostname.includes('github.com')) {
      if (fillGitHubField(field.element, content, field.name)) {
        return
      }
    }

    // 检查组件类型
    const isReactComponent = field.element.classList.contains('fui-Input__input') ||
                             field.element.hasAttribute('data-reactid') ||
                             !!field.element.closest('[data-reactroot]')

    const isRichTextEditor = field.element.getAttribute('contenteditable') === 'true' ||
                             field.element.classList.contains('ck-editor__editable') ||
                             field.element.classList.contains('tox-edit-area__iframe') ||
                             field.element.classList.contains('mce-content-body')

    const isIframeEditor = field.element.tagName === 'IFRAME' &&
                          (field.element.classList.contains('cke_wysiwyg_frame') ||
                           field.element.classList.contains('tox-edit-area__iframe'))

    // 处理不同类型的元素
    if (isIframeEditor) {
      fillIframeEditor(field.element as HTMLIFrameElement, content)
    } else if (isRichTextEditor) {
      fillRichTextEditor(field.element, content, field)
    } else if (isReactComponent && (field.element instanceof HTMLInputElement || field.element instanceof HTMLTextAreaElement)) {
      fillReactComponent(field.element, content)
    } else if (field.element instanceof HTMLInputElement) {
      fillInputElement(field.element, content)
    } else if (field.element instanceof HTMLTextAreaElement) {
      field.element.value = content
      triggerEvents(field.element, ['input', 'change'])
    } else if (field.element instanceof HTMLSelectElement) {
      fillSelectElement(field.element, content)
    } else {
      // 其他类型的元素
      field.element.textContent = content
      triggerEvents(field.element, ['input', 'change'])
    }

    // 记录填充后的值
    const newValue = field.element instanceof HTMLInputElement || field.element instanceof HTMLTextAreaElement
      ? field.element.value
      : field.element.textContent

    Logger.debug('fill', `Field state after filling:`, {
      fieldName: field.name,
      newValue,
      valueChanged: newValue !== previousValue
    })

    Logger.debug('fill', `Successfully filled field: ${field.name}`)
  } catch (error) {
    Logger.error(`Error filling field ${field.name}:`, error)
  }
}

/**
 * 填充 input 元素
 */
function fillInputElement(input: HTMLInputElement, content: string): void {
  try {
    switch (input.type.toLowerCase()) {
      case 'checkbox':
        const shouldCheck = /^(yes|true|1|on|checked|selected|enable|enabled)$/i.test(content.trim())
        input.checked = shouldCheck
        break

      case 'radio':
        if (input.value.toLowerCase() === content.toLowerCase()) {
          input.checked = true
        } else {
          // 查找同名的其他单选按钮
          const radioGroup = document.querySelectorAll(`input[type="radio"][name="${input.name}"]`)
          for (const radio of Array.from(radioGroup)) {
            if ((radio as HTMLInputElement).value.toLowerCase() === content.toLowerCase()) {
              (radio as HTMLInputElement).checked = true
              break
            }
          }
        }
        break

      case 'date':
        try {
          const date = new Date(content)
          if (!isNaN(date.getTime())) {
            const year = date.getFullYear()
            const month = String(date.getMonth() + 1).padStart(2, '0')
            const day = String(date.getDate()).padStart(2, '0')
            input.value = `${year}-${month}-${day}`
          } else {
            input.value = content
          }
        } catch (e) {
          input.value = content
        }
        break

      default:
        input.value = content
    }

    triggerEvents(input, ['input', 'change'])
  } catch (error) {
    Logger.error('Error filling input element:', error)
  }
}

/**
 * 触发事件
 */
function triggerEvents(element: Element, eventTypes: string[]): void {
  try {
    eventTypes.forEach(eventType => {
      const event = new Event(eventType, { bubbles: true })
      element.dispatchEvent(event)
    })
  } catch (error) {
    Logger.error('Error triggering events:', error)
  }
}

/**
 * 主要的表单填充函数
 */
export function fillForm(formContent: FormContent, callback?: FillFormCallback): void {
  try {
    Logger.debug('fill', 'Starting form fill with content:', formContent)

    // 解析 JSON 内容（如果需要）
    let contentToUse = formContent
    if (typeof formContent === 'object' && formContent.content && typeof formContent.content === 'string') {
      try {
        const parsedContent = JSON.parse(formContent.content)
        contentToUse = parsedContent
        Logger.debug('fill', 'Successfully parsed JSON content')
      } catch (e) {
        Logger.debug('fill', 'Using original content due to JSON parse failure')
      }
    }

    Logger.debug('fill', 'Form content structure:', {
      hasContent: !!contentToUse,
      contentType: typeof contentToUse,
      keys: contentToUse ? Object.keys(contentToUse) : []
    })

    // 移除生成中的视觉效果
    removeGeneratingEffect()

    // 检测表单字段
    const formFields = detectFormFields()
    if (!formFields.length) {
      throw new Error('No form fields detected')
    }

    // 填充字段
    formFields.forEach(field => {
      try {
        let content: string | undefined

        // 根据字段类型匹配内容
        if (field.name === 'recipient' || field.name === 'to') {
          content = findValueByLanguage(contentToUse, 'recipient') ||
                   findValueByLanguage(contentToUse, 'to') ||
                   findValueByLanguage(contentToUse, 'email')
        } else if (field.name === 'subject') {
          content = findValueByLanguage(contentToUse, 'subject') ||
                   findValueByLanguage(contentToUse, 'title')
        } else if (field.name === 'body') {
          content = findValueByLanguage(contentToUse, 'body') ||
                   findValueByLanguage(contentToUse, 'content') ||
                   findValueByLanguage(contentToUse, 'message')
        } else if (field.name === 'title' || field.name === 'description') {
          // GitHub 字段特殊处理
          if (field.name === 'title') {
            const titleKeys = ['title', 'name', 'summary', 'subject']
            for (const key of titleKeys) {
              content = findValueByLanguage(contentToUse, key)
              if (content) break
            }
          } else {
            const descKeys = ['description', 'body', 'content', 'details', 'comment', 'message']
            for (const key of descKeys) {
              content = findValueByLanguage(contentToUse, key)
              if (content) break
            }
          }
        } else {
          // 通用字段匹配
          const labelNoAsterisk = field.label?.replace(/\s*\*\s*$/, '') || ''
          const labelFirstWord = field.label?.split(/[\s*]/)[0] || ''

          content = findValueByLanguage(contentToUse, field.name) ||
                   findValueByLanguage(contentToUse, field.id) ||
                   findValueByLanguage(contentToUse, labelNoAsterisk) ||
                   findValueByLanguage(contentToUse, labelFirstWord) ||
                   findValueByLanguage(contentToUse, field.placeholder || '')
        }

        if (content) {
          fillFieldContent(field, content)
        }
      } catch (e) {
        Logger.error(`Error filling field ${field.name}:`, e)
      }
    })

    // 成功回调
    if (callback?.success) {
      callback.success()
    }
  } catch (error) {
    Logger.error('Error filling form:', error)

    // 发送错误消息到content script显示Toast
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.sendMessage({
        type: 'showToast',
        message: 'Failed to fill form fields. Please try again.',
        toastType: 'error'
      }).catch(() => {
        // 忽略消息发送失败
      });
    }

    if (callback?.error) {
      callback.error(error)
    }
  }
}