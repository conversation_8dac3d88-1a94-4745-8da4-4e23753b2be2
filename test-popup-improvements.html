<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Popup Improvements</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 40px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .form-container {
            max-width: 500px;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #1D5DF4;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .no-forms {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h1>Fillify Popup Improvements Test</h1>
    
    <div class="test-section">
        <h2>Test Case 1: Page with Forms</h2>
        <p>This section contains fillable forms to test normal popup behavior.</p>
        <div class="form-container">
            <form>
                <input type="text" name="name" placeholder="Full Name">
                <input type="email" name="email" placeholder="Email Address">
                <textarea name="message" placeholder="Message" rows="4"></textarea>
                <select name="country">
                    <option value="">Select Country</option>
                    <option value="us">United States</option>
                    <option value="uk">United Kingdom</option>
                    <option value="ca">Canada</option>
                </select>
                <button type="submit">Submit</button>
            </form>
        </div>
    </div>

    <div class="test-section">
        <h2>Test Case 2: Mode Switching</h2>
        <p>Use the Fillify popup to test mode switching (General → Email → Bug Report).</p>
        <p><strong>Expected:</strong> Disabled state should persist when switching modes if no forms are detected.</p>
    </div>

    <div class="test-section">
        <h2>Test Case 3: Login UI</h2>
        <p>Test the login prompt UI improvements.</p>
        <p><strong>Expected:</strong> Better styling, hover effects, and improved layout.</p>
    </div>

    <div class="test-section">
        <h2>Test Case 4: Toast Messages</h2>
        <p>Test various error scenarios:</p>
        <ul>
            <li>No forms detected (should show info toast)</li>
            <li>AI API errors (should show specific error messages)</li>
            <li>Network errors (should show network-specific messages)</li>
            <li>Form filling failures (should show error toast)</li>
        </ul>
    </div>

    <div class="test-section no-forms">
        <h2>Test Case 5: Page Without Forms</h2>
        <p>This section intentionally has no form elements.</p>
        <p><strong>Expected:</strong> Popup should show "No fillable forms detected" toast and remain disabled.</p>
    </div>

    <script>
        // Add some dynamic content to test form detection
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            console.log('Forms detected:', document.querySelectorAll('form').length);
            console.log('Input fields detected:', document.querySelectorAll('input, textarea, select').length);
        });

        // Simulate some dynamic form creation after page load
        setTimeout(() => {
            const dynamicSection = document.createElement('div');
            dynamicSection.className = 'test-section';
            dynamicSection.innerHTML = `
                <h2>Test Case 6: Dynamic Forms</h2>
                <p>This form was added dynamically after page load.</p>
                <form>
                    <input type="text" name="dynamic-field" placeholder="Dynamic Field">
                    <button type="submit">Dynamic Submit</button>
                </form>
            `;
            document.body.appendChild(dynamicSection);
            console.log('Dynamic form added');
        }, 2000);
    </script>
</body>
</html>
